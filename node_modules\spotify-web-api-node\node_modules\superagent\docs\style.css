body {
  padding: 40px 80px;
  font: 14px/1.5 "Helvetica Neue", Helvetica, sans-serif;
  background: #181818 url(images/bg.png);
  text-align: center;
}

#content {
  margin: 0 auto;
  padding: 10px 40px;
  text-align: left;
  background: white;
  width: 50%;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0 2px 5px 0 black;
}

#menu {
  font-size: 13px;
  margin: 0;
  padding: 0;
  text-align: left;
  position: fixed;
  top: 15px;
  left: 15px;
}

#menu ul {
  margin: 0;
  padding: 0;
}

#menu li {
  list-style: none;
}

#menu a {
  color: rgba(255,255,255,.5);
  text-decoration: none;
}

#menu a:hover {
  color: white;
}

#menu .active a {
  color: white;
}

pre {
  padding: 10px;
}

code {
  font-family: monaco, monospace, sans-serif;
  font-size: 0.85em;
}

p code {
  border: 1px solid #ECEA75;
  padding: 1px 3px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  background: #FDFCD1;
}

pre {
  padding: 20px 25px;
  border: 1px solid #ddd;
  -webkit-box-shadow: inset 0 0 5px #eee;
  -moz-box-shadow: inset 0 0 5px #eee;
  box-shadow: inset 0 0 5px #eee;
}

code .comment { color: #ddd }
code .init { color: #2F6FAD }
code .string { color: #5890AD }
code .keyword { color: #8A6343 }
code .number { color: #2F6FAD }

/* override tocbot style to avoid vertical white line in table of content */
.toc-link::before {
  content: initial;
}

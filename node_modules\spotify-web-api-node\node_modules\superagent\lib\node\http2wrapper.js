"use strict";

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var Stream = require('stream');

var util = require('util');

var net = require('net');

var tls = require('tls'); // eslint-disable-next-line node/no-deprecated-api


var _require = require('url'),
    parse = _require.parse;

var semver = require('semver');

var http2; // eslint-disable-next-line node/no-unsupported-features/node-builtins

if (semver.gte(process.version, 'v10.10.0')) http2 = require('http2');else throw new Error('superagent: this version of Node.js does not support http2');
var _http2$constants = http2.constants,
    HTTP2_HEADER_PATH = _http2$constants.HTTP2_HEADER_PATH,
    HTTP2_HEADER_STATUS = _http2$constants.HTTP2_HEADER_STATUS,
    HTTP2_HEADER_METHOD = _http2$constants.HTTP2_HEADER_METHOD,
    HTTP2_HEADER_AUTHORITY = _http2$constants.HTTP2_HEADER_AUTHORITY,
    HTTP2_HEADER_HOST = _http2$constants.HTTP2_HEADER_HOST,
    HTTP2_HEADER_SET_COOKIE = _http2$constants.HTTP2_HEADER_SET_COOKIE,
    NGHTTP2_CANCEL = _http2$constants.NGHTTP2_CANCEL;

function setProtocol(protocol) {
  return {
    request: function request(options) {
      return new Request(protocol, options);
    }
  };
}

function Request(protocol, options) {
  var _this = this;

  Stream.call(this);
  var defaultPort = protocol === 'https:' ? 443 : 80;
  var defaultHost = 'localhost';
  var port = options.port || defaultPort;
  var host = options.host || defaultHost;
  delete options.port;
  delete options.host;
  this.method = options.method;
  this.path = options.path;
  this.protocol = protocol;
  this.host = host;
  delete options.method;
  delete options.path;

  var sessionOptions = _objectSpread({}, options);

  if (options.socketPath) {
    sessionOptions.socketPath = options.socketPath;
    sessionOptions.createConnection = this.createUnixConnection.bind(this);
  }

  this._headers = {};
  var session = http2.connect("".concat(protocol, "//").concat(host, ":").concat(port), sessionOptions);
  this.setHeader('host', "".concat(host, ":").concat(port));
  session.on('error', function (err) {
    return _this.emit('error', err);
  });
  this.session = session;
}
/**
 * Inherit from `Stream` (which inherits from `EventEmitter`).
 */


util.inherits(Request, Stream);

Request.prototype.createUnixConnection = function (authority, options) {
  switch (this.protocol) {
    case 'http:':
      return net.connect(options.socketPath);

    case 'https:':
      options.ALPNProtocols = ['h2'];
      options.servername = this.host;
      options.allowHalfOpen = true;
      return tls.connect(options.socketPath, options);

    default:
      throw new Error('Unsupported protocol', this.protocol);
  }
}; // eslint-disable-next-line no-unused-vars


Request.prototype.setNoDelay = function (bool) {// We can not use setNoDelay with HTTP/2.
  // Node 10 limits http2session.socket methods to ones safe to use with HTTP/2.
  // See also https://nodejs.org/api/http2.html#http2_http2session_socket
};

Request.prototype.getFrame = function () {
  var _method,
      _this2 = this;

  if (this.frame) {
    return this.frame;
  }

  var method = (_method = {}, _defineProperty(_method, HTTP2_HEADER_PATH, this.path), _defineProperty(_method, HTTP2_HEADER_METHOD, this.method), _method);
  var headers = this.mapToHttp2Header(this._headers);
  headers = Object.assign(headers, method);
  var frame = this.session.request(headers); // eslint-disable-next-line no-unused-vars

  frame.once('response', function (headers, flags) {
    headers = _this2.mapToHttpHeader(headers);
    frame.headers = headers;
    frame.statusCode = headers[HTTP2_HEADER_STATUS];
    frame.status = frame.statusCode;

    _this2.emit('response', frame);
  });
  this._headerSent = true;
  frame.once('drain', function () {
    return _this2.emit('drain');
  });
  frame.on('error', function (err) {
    return _this2.emit('error', err);
  });
  frame.on('close', function () {
    return _this2.session.close();
  });
  this.frame = frame;
  return frame;
};

Request.prototype.mapToHttpHeader = function (headers) {
  var keys = Object.keys(headers);
  var http2Headers = {};

  for (var _i = 0, _keys = keys; _i < _keys.length; _i++) {
    var key = _keys[_i];
    var value = headers[key];
    key = key.toLowerCase();

    switch (key) {
      case HTTP2_HEADER_SET_COOKIE:
        value = Array.isArray(value) ? value : [value];
        break;

      default:
        break;
    }

    http2Headers[key] = value;
  }

  return http2Headers;
};

Request.prototype.mapToHttp2Header = function (headers) {
  var keys = Object.keys(headers);
  var http2Headers = {};

  for (var _i2 = 0, _keys2 = keys; _i2 < _keys2.length; _i2++) {
    var key = _keys2[_i2];
    var value = headers[key];
    key = key.toLowerCase();

    switch (key) {
      case HTTP2_HEADER_HOST:
        key = HTTP2_HEADER_AUTHORITY;
        value = /^http:\/\/|^https:\/\//.test(value) ? parse(value).host : value;
        break;

      default:
        break;
    }

    http2Headers[key] = value;
  }

  return http2Headers;
};

Request.prototype.setHeader = function (name, value) {
  this._headers[name.toLowerCase()] = value;
};

Request.prototype.getHeader = function (name) {
  return this._headers[name.toLowerCase()];
};

Request.prototype.write = function (data, encoding) {
  var frame = this.getFrame();
  return frame.write(data, encoding);
};

Request.prototype.pipe = function (stream, options) {
  var frame = this.getFrame();
  return frame.pipe(stream, options);
};

Request.prototype.end = function (data) {
  var frame = this.getFrame();
  frame.end(data);
}; // eslint-disable-next-line no-unused-vars


Request.prototype.abort = function (data) {
  var frame = this.getFrame();
  frame.close(NGHTTP2_CANCEL);
  this.session.destroy();
};

exports.setProtocol = setProtocol;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
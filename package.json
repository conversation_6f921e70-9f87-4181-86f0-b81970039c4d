{"dependencies": {"@spotify/web-api-ts-sdk": "^1.2.0", "@types/spotify-api": "^0.0.25", "dotenv": "^17.2.2", "node-fetch": "^3.3.2", "spotify": "^0.3.0", "spotify-playlist-downloader": "^0.1.4", "spotify-web-api-node": "^5.0.2"}, "name": "artist-releases-downloader", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node main.ts", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.3.1", "@types/node-fetch": "^2.6.13", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}
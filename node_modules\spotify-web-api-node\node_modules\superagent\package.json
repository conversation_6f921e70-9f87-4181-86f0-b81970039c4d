{"name": "superagent", "description": "elegant & feature rich browser / node HTTP with a fluent API", "version": "6.1.0", "author": "<PERSON><PERSON> <<EMAIL>>", "browser": {"./src/node/index.js": "./src/client.js", "./lib/node/index.js": "./lib/client.js", "./test/support/server.js": "./test/support/blank.js"}, "bugs": {"url": "https://github.com/visionmedia/superagent/issues"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Loftis <<EMAIL>>", "<PERSON> <<EMAIL>>"], "dependencies": {"component-emitter": "^1.3.0", "cookiejar": "^2.1.2", "debug": "^4.1.1", "fast-safe-stringify": "^2.0.7", "form-data": "^3.0.0", "formidable": "^1.2.2", "methods": "^1.1.2", "mime": "^2.4.6", "qs": "^6.9.4", "readable-stream": "^3.6.0", "semver": "^7.3.2"}, "devDependencies": {"@babel/cli": "^7.10.5", "@babel/core": "^7.11.1", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/preset-env": "^7.11.0", "@commitlint/cli": "^9.1.1", "@commitlint/config-conventional": "^9.1.1", "Base64": "^1.1.0", "babelify": "^10.0.0", "basic-auth-connect": "^1.0.0", "body-parser": "^1.19.0", "browserify": "^16.5.2", "codecov": "^3.7.2", "cookie-parser": "^1.4.5", "cross-env": "^7.0.2", "eslint": "^7.6.0", "eslint-config-xo-lass": "^1.0.3", "eslint-plugin-compat": "^3.8.0", "eslint-plugin-node": "^11.1.0", "express": "^4.17.1", "express-session": "^1.17.1", "fixpack": "^3.0.6", "husky": "^4.2.5", "lint-staged": "^10.2.11", "marked": "^1.1.1", "mocha": "3.5.3", "multer": "^1.4.2", "nyc": "^15.1.0", "remark-cli": "^8.0.1", "remark-preset-github": "^3.0.0", "rimraf": "^3.0.2", "should": "^13.2.3", "should-http": "^0.1.1", "tinyify": "^3.0.0", "xo": "0.32.1", "zuul": "^3.12.0"}, "engines": {"node": ">= 7.0.0"}, "homepage": "https://github.com/visionmedia/superagent", "husky": {"hooks": {"pre-commit": "npm test", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "jsdelivr": "dist/superagent.min.js", "keywords": ["agent", "ajax", "ajax", "api", "async", "await", "axios", "cancel", "client", "frisbee", "got", "http", "http", "https", "ky", "promise", "promise", "promises", "request", "request", "requests", "response", "rest", "retry", "super", "superagent", "timeout", "transform", "xhr", "xmlhttprequest"], "license": "MIT", "lint-staged": {"linters": {"*.js": ["xo --fix", "git add"], "*.md": ["remark . -qfo", "git add"], "package.json": ["fixpack", "git add"]}}, "main": "lib/node/index.js", "prettier": {"singleQuote": true, "bracketSpacing": true, "trailingComma": "none"}, "remarkConfig": {"plugins": ["preset-github"]}, "repository": {"type": "git", "url": "git://github.com/visionmedia/superagent.git"}, "scripts": {"browserify": "browserify src/node/index.js -o dist/superagent.js -s superagent -g [ babelify --configFile ./.dist.babelrc ]", "build": "npm run build:clean && npm run build:lib && npm run build:dist", "build:clean": "<PERSON><PERSON><PERSON> lib dist", "build:dist": "npm run browserify && npm run minify", "build:lib": "babel --config-file ./.lib.babelrc src --out-dir lib", "coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "lint": "xo && remark . -qfo && eslint -c .lib.eslintrc lib && eslint -c .dist.eslintrc dist", "minify": "cross-env NODE_ENV=production browserify src/node/index.js -o dist/superagent.min.js -s superagent -g [ babelify --configFile ./.dist.babelrc ] -p tinyify", "nyc": "cross-env NODE_ENV=test nyc ava", "test": "npm run build && npm run lint && make test", "test-http2": "npm run build && npm run lint && make test-node-http2"}, "unpkg": "dist/superagent.min.js", "xo": {"prettier": true, "space": true, "extends": ["xo-lass"], "env": ["node", "browser"], "overrides": [{"files": "test/**/*.js", "env": ["mocha"], "rules": {"block-scoped-var": "off", "complexity": "off", "default-case": "off", "eqeqeq": "off", "func-name-matching": "off", "func-names": "off", "guard-for-in": "off", "handle-callback-err": "off", "import/no-extraneous-dependencies": "off", "import/no-unassigned-import": "off", "import/order": "off", "max-nested-callbacks": "off", "new-cap": "off", "no-eq-null": "off", "no-extend-native": "off", "no-implicit-coercion": "off", "no-multi-assign": "off", "no-negated-condition": "off", "no-prototype-builtins": "off", "no-redeclare": "off", "no-undef": "off", "no-unused-expressions": "off", "no-unused-vars": "off", "no-use-extend-native/no-use-extend-native": "off", "no-useless-escape": "off", "no-var": "off", "no-void": "off", "node/no-deprecated-api": "off", "prefer-rest-params": "off", "prefer-spread": "off", "promise/prefer-await-to-then": "off", "promise/valid-params": "off", "unicorn/filename-case": "off", "valid-jsdoc": "off", "node/no-unsupported-features/node-builtins": "off", "node/no-path-concat": "off"}}], "rules": {"unicorn/prevent-abbreviations": "off", "node/no-unsupported-features/es-syntax": "off"}, "globals": ["ActiveXObject"]}}
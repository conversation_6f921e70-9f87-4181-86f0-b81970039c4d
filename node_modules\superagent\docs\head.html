<!DOCTYPE html>
<html>
  <head>
    <title>Superagent</title>
    <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.4.3/jquery.min.js"></script>
    <style>
      body {
        font: 16px/1.6 "Helvetica Neue", arial, sans-serif;
        padding: 60px;
      }
      pre { font-size: 14px; line-height: 1.3 }
      code .init { color: #2F6FAD }
      code .string { color: #5890AD }
      code .keyword { color: #8A6343 }
      code .number { color: #2F6FAD }
    </style>
    <script>
      $(function(){
        $('code').each(function(){
          $(this).html(highlight($(this).text()));
        });
      });

      function highlight(js) {
        return js
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/\/\/(.*)/gm, '<span class="comment">//$1</span>')
          .replace(/('.*')/gm, '<span class="string">$1</span>')
          .replace(/(\d+\.\d+)/gm, '<span class="number">$1</span>')
          .replace(/(\d+)/gm, '<span class="number">$1</span>')
          .replace(/\bnew *(\w+)/gm, '<span class="keyword">new</span> <span class="init">$1</span>')
          .replace(/\b(function|new|throw|return|var|if|else)\b/gm, '<span class="keyword">$1</span>')
      }
    </script>
  </head>
  <body>
    <h1>Superagent</h1>
    <p>The superagent test suite.</p>

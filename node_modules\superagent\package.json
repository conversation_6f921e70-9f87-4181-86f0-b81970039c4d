{"name": "superagent", "version": "0.12.4", "description": "elegant & feature rich browser / node HTTP with a fluent API", "keywords": ["http", "ajax", "request", "agent"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> Loftis <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/visionmedia/superagent.git"}, "dependencies": {"qs": "0.5.2", "formidable": "1.0.9", "mime": "1.2.5", "emitter-component": "0.0.6", "methods": "0.0.1", "cookiejar": "1.3.0"}, "devDependencies": {"express": "3.0.3", "better-assert": "~0.1.0", "should": "*", "mocha": "*"}, "component": {"scripts": {"superagent": "lib/client.js"}}, "main": "lib/node", "engines": {"node": "*"}}
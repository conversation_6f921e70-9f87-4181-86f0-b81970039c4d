"use strict";

/**
 * Module dependencies.
 */
// eslint-disable-next-line node/no-deprecated-api
var _require = require('url'),
    parse = _require.parse;

var _require2 = require('cookiejar'),
    CookieJar = _require2.CookieJar;

var _require3 = require('cookiejar'),
    CookieAccessInfo = _require3.CookieAccessInfo;

var methods = require('methods');

var request = require('../..');

var AgentBase = require('../agent-base');
/**
 * Expose `Agent`.
 */


module.exports = Agent;
/**
 * Initialize a new `Agent`.
 *
 * @api public
 */

function Agent(options) {
  if (!(this instanceof Agent)) {
    return new Agent(options);
  }

  AgentBase.call(this);
  this.jar = new CookieJar();

  if (options) {
    if (options.ca) {
      this.ca(options.ca);
    }

    if (options.key) {
      this.key(options.key);
    }

    if (options.pfx) {
      this.pfx(options.pfx);
    }

    if (options.cert) {
      this.cert(options.cert);
    }

    if (options.rejectUnauthorized === false) {
      this.disableTLSCerts();
    }
  }
}

Agent.prototype = Object.create(AgentBase.prototype);
/**
 * Save the cookies in the given `res` to
 * the agent's cookie jar for persistence.
 *
 * @param {Response} res
 * @api private
 */

Agent.prototype._saveCookies = function (res) {
  var cookies = res.headers['set-cookie'];
  if (cookies) this.jar.setCookies(cookies);
};
/**
 * Attach cookies when available to the given `req`.
 *
 * @param {Request} req
 * @api private
 */


Agent.prototype._attachCookies = function (req) {
  var url = parse(req.url);
  var access = new CookieAccessInfo(url.hostname, url.pathname, url.protocol === 'https:');
  var cookies = this.jar.getCookies(access).toValueString();
  req.cookies = cookies;
};

methods.forEach(function (name) {
  var method = name.toUpperCase();

  Agent.prototype[name] = function (url, fn) {
    var req = new request.Request(method, url);
    req.on('response', this._saveCookies.bind(this));
    req.on('redirect', this._saveCookies.bind(this));
    req.on('redirect', this._attachCookies.bind(this, req));

    this._setDefaults(req);

    this._attachCookies(req);

    if (fn) {
      req.end(fn);
    }

    return req;
  };
});
Agent.prototype.del = Agent.prototype.delete;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
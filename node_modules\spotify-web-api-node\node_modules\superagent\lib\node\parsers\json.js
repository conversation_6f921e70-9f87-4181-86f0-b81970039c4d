"use strict";

module.exports = function (res, fn) {
  res.text = '';
  res.setEncoding('utf8');
  res.on('data', function (chunk) {
    res.text += chunk;
  });
  res.on('end', function () {
    var body;
    var err;

    try {
      body = res.text && JSON.parse(res.text);
    } catch (err_) {
      err = err_; // issue #675: return the raw response if the response parsing fails

      err.rawResponse = res.text || null; // issue #876: return the http status code if the response parsing fails

      err.statusCode = res.statusCode;
    } finally {
      fn(err, body);
    }
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
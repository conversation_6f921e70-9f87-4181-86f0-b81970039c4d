{"name": "qs", "description": "querystring parser", "version": "0.5.2", "keywords": ["query string", "parser", "component"], "repository": {"type": "git", "url": "git://github.com/visionmedia/node-querystring.git"}, "devDependencies": {"mocha": "*", "expect.js": "*"}, "component": {"scripts": {"querystring": "querystring.js"}}, "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "main": "index", "engines": {"node": "*"}}
"use strict";

/**
 * Module dependencies.
 */
var _require = require('string_decoder'),
    StringDecoder = _require.StringDecoder;

var Stream = require('stream');

var zlib = require('zlib');
/**
 * Buffers response data events and re-emits when they're unzipped.
 *
 * @param {Request} req
 * @param {Response} res
 * @api private
 */


exports.unzip = function (req, res) {
  var unzip = zlib.createUnzip();
  var stream = new Stream();
  var decoder; // make node responseOnEnd() happy

  stream.req = req;
  unzip.on('error', function (err) {
    if (err && err.code === 'Z_BUF_ERROR') {
      // unexpected end of file is ignored by browsers and curl
      stream.emit('end');
      return;
    }

    stream.emit('error', err);
  }); // pipe to unzip

  res.pipe(unzip); // override `setEncoding` to capture encoding

  res.setEncoding = function (type) {
    decoder = new StringDecoder(type);
  }; // decode upon decompressing with captured encoding


  unzip.on('data', function (buf) {
    if (decoder) {
      var str = decoder.write(buf);
      if (str.length > 0) stream.emit('data', str);
    } else {
      stream.emit('data', buf);
    }
  });
  unzip.on('end', function () {
    stream.emit('end');
  }); // override `on` to capture data listeners

  var _on = res.on;

  res.on = function (type, fn) {
    if (type === 'data' || type === 'end') {
      stream.on(type, fn.bind(res));
    } else if (type === 'error') {
      stream.on(type, fn.bind(res));

      _on.call(res, type, fn);
    } else {
      _on.call(res, type, fn);
    }

    return this;
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
import fetch from 'node-fetch';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config();

interface SpotifyTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

interface SpotifyAlbum {
  album_type: string;
  total_tracks: number;
  available_markets: string[];
  external_urls: {
    spotify: string;
  };
  href: string;
  id: string;
  images: Array<{
    url: string;
    height: number;
    width: number;
  }>;
  name: string;
  release_date: string;
  release_date_precision: string;
  restrictions?: {
    reason: string;
  };
  type: string;
  uri: string;
  artists: Array<{
    external_urls: {
      spotify: string;
    };
    href: string;
    id: string;
    name: string;
    type: string;
    uri: string;
  }>;
  album_group?: string;
}

interface SpotifyAlbumsResponse {
  href: string;
  limit: number;
  next: string | null;
  offset: number;
  previous: string | null;
  total: number;
  items: SpotifyAlbum[];
}

async function getSpotifyToken(): Promise<string> {
  const clientId = process.env.SPOTIFY_CLIENT_ID;
  const clientSecret = process.env.SPOTIFY_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET must be set in .env file');
  }

  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    throw new Error(`Failed to get Spotify token: ${response.status} ${response.statusText}`);
  }

  const data = await response.json() as SpotifyTokenResponse;
  return data.access_token;
}

async function getArtistAlbums(
  artistId: string,
  options: {
    includeGroups?: string;
    market?: string;
    limit?: number;
    offset?: number;
  } = {}
): Promise<SpotifyAlbumsResponse> {
  const token = await getSpotifyToken();

  // Build query parameters
  const params = new URLSearchParams();
  if (options.includeGroups) params.append('include_groups', options.includeGroups);
  if (options.market) params.append('market', options.market);
  if (options.limit) params.append('limit', options.limit.toString());
  if (options.offset) params.append('offset', options.offset.toString());

  const url = `https://api.spotify.com/v1/artists/${artistId}/albums${params.toString() ? '?' + params.toString() : ''}`;

  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch artist albums: ${response.status} ${response.statusText}`);
  }

  const data = await response.json() as SpotifyAlbumsResponse;
  return data;
}

async function saveToJson(data: any, filename: string): Promise<void> {
  const outputDir = path.join(__dirname, 'output');

  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const filePath = path.join(outputDir, filename);
  const jsonData = JSON.stringify(data, null, 2);

  fs.writeFileSync(filePath, jsonData, 'utf8');
  console.log(`Data saved to: ${filePath}`);
}

async function getAllArtistAlbums(artistId: string, artistName?: string): Promise<SpotifyAlbum[]> {
  const allAlbums: SpotifyAlbum[] = [];
  let offset = 0;
  const limit = 50; // Maximum allowed by Spotify API

  console.log(`Fetching albums for artist ID: ${artistId}`);

  while (true) {
    const response = await getArtistAlbums(artistId, {
      includeGroups: 'album,single,appears_on,compilation',
      limit,
      offset
    });

    allAlbums.push(...response.items);
    console.log(`Fetched ${response.items.length} albums (total: ${allAlbums.length}/${response.total})`);

    // If we've fetched all albums or there are no more, break
    if (response.items.length < limit || !response.next) {
      break;
    }

    offset += limit;
  }

  // Save all albums to JSON
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${artistName || artistId}_albums_${timestamp}.json`;
  await saveToJson({
    artist_id: artistId,
    artist_name: artistName,
    total_albums: allAlbums.length,
    fetched_at: new Date().toISOString(),
    albums: allAlbums
  }, filename);

  return allAlbums;
}

// Example usage
async function main() {
  try {
    // Example artist IDs:
    // The Weeknd: '1Xyo4u8uXC1ZmMpatF05PJ'
    // Taylor Swift: '06HL4z0CvFAxyc27GXpf02'
    // Drake: '3TVXtAsR1Inumwj472S9r4'

    const artistId = '1Xyo4u8uXC1ZmMpatF05PJ'; // The Weeknd
    const artistName = 'The Weeknd';

    const albums = await getAllArtistAlbums(artistId, artistName);

    console.log(`\nSuccessfully fetched ${albums.length} albums for ${artistName}`);
    console.log('\nLatest releases:');

    // Show the 5 most recent releases
    const sortedAlbums = albums
      .sort((a, b) => new Date(b.release_date).getTime() - new Date(a.release_date).getTime())
      .slice(0, 5);

    sortedAlbums.forEach((album, index) => {
      console.log(`${index + 1}. ${album.name} (${album.release_date}) - ${album.album_type}`);
    });

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the main function
main();